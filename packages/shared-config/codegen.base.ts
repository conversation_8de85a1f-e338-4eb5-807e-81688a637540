import { type CodegenConfig } from "@graphql-codegen/cli"

/**
 * Base configuration for GraphQL Code Generator
 * This provides common settings that can be extended by individual apps
 */

export interface BaseCodegenOptions {
  /**
   * The GraphQL schema URL or file path
   */
  schema: string
  /**
   * Document patterns to include
   */
  documents: string[]
  /**
   * Output directory for generated files
   */
  outputDir: string
  /**
   * Whether to enable persisted queries
   * @default false
   */
  enablePersistedQueries?: boolean
  /**
   * Whether to include fallback query in development
   * @default true in development, false in production
   */
  includeFallbackQuery?: boolean
  /**
   * Hash algorithm for persisted queries
   * @default "sha256"
   */
  hashAlgorithm?: "sha256" | "sha1"
  /**
   * Additional preset configuration
   */
  additionalPresetConfig?: Record<string, any>
}

/**
 * Create a standardized codegen configuration
 */
export function createCodegenConfig(options: BaseCodegenOptions): CodegenConfig {
  const {
    schema,
    documents,
    outputDir,
    enablePersistedQueries = false,
    includeFallbackQuery = process.env.NODE_ENV !== "production",
    hashAlgorithm = "sha256",
    additionalPresetConfig = {},
  } = options

  const baseConfig: CodegenConfig = {
    watch: true,
    ignoreNoDocuments: true, // Better experience with the watcher
    generates: {
      [outputDir]: {
        preset: "client",
        documents,
        schema,
        presetConfig: {
          ...additionalPresetConfig,
          // Enable persisted queries if requested
          ...(enablePersistedQueries && {
            persistedDocuments: {
              mode: "replaceDocument",
              hashAlgorithm,
            },
          }),
        },
      },
    },
  }

  return baseConfig
}

/**
 * Common document patterns for different app types
 */
export const DOCUMENT_PATTERNS = {
  /**
   * Standard federated pattern used by most apps
   */
  FEDERATED: [
    "src/**/federated/mutations.ts",
    "src/**/federated/queries.ts",
  ],
  /**
   * Chat-specific pattern
   */
  CHAT: ["src/chat-ui/**/graphql/*.ts"],
  /**
   * Blacklist-specific pattern
   */
  BLACKLIST: [
    "src/**/blacklist-federated/mutations.ts",
    "src/**/blacklist-federated/queries.ts",
  ],
  /**
   * All GraphQL files pattern
   */
  ALL: ["src/**/*.graphql", "src/**/*.gql"],
} as const

/**
 * Common schema URLs for different environments
 */
export const SCHEMA_URLS = {
  ACCOUNT: {
    development: "https://account.eproc.dev/graphql",
    staging: "https://account.eproc.dev/graphql",
    production: "${NEXT_PUBLIC_ACCOUNT_URL}graphql",
  },
  BUYER: {
    development: "https://buyer.eproc.dev/graphql",
    staging: "https://buyer.eproc.dev/graphql", 
    production: "${NEXT_PUBLIC_AUTH_BASE_URL_BUYER}graphql",
  },
  SELLER: {
    development: "https://seller.eproc.dev/graphql",
    staging: "https://seller.eproc.dev/graphql",
    production: "${NEXT_PUBLIC_AUTH_BASE_URL_SELLER}graphql",
  },
  INTERNAL: {
    development: "https://admin.eproc.dev/graphql",
    staging: "https://admin.eproc.dev/graphql",
    production: "${NEXT_PUBLIC_AUTH_BASE_URL_INTERNAL}graphql",
  },
  VERIFICATOR: {
    development: "https://verificator.eproc.dev/graphql",
    staging: "https://verificator.eproc.dev/graphql",
    production: "${NEXT_PUBLIC_VERIFICATOR_URL}graphql",
  },
  BLACKLIST: {
    development: "https://blacklist.eproc.dev/graphql",
    staging: "https://blacklist.eproc.dev/graphql",
    production: "${NEXT_PUBLIC_BLACKLIST_URL}graphql",
  },
} as const

/**
 * Get schema URL based on environment
 */
export function getSchemaUrl(
  service: keyof typeof SCHEMA_URLS,
  environment: "development" | "staging" | "production" = "development"
): string {
  return SCHEMA_URLS[service][environment]
}
